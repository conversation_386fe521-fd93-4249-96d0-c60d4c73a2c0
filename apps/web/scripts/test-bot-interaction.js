#!/usr/bin/env node

/**
 * Telegram Bot Interaction Test
 * 
 * Simulates real user interactions with the bot to test functionality
 */

const https = require('https');

const WEBHOOK_URL = 'https://www.buddychip.app/api/telegram/webhook';
const SECRET_TOKEN = 'fedb2719f4a14793c848016d95e7378dcd7fab158f6ac7cd46ea266d645989d9';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function log(color, message, prefix = '') {
  console.log(`${colors[color]}${prefix}${message}${colors.reset}`);
}

function makeRequest(url, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'User-Agent': 'BuddyChip-Bot-Test/1.0',
        ...headers
      }
    };

    if (data && method !== 'GET') {
      options.headers['Content-Type'] = 'application/json';
      options.headers['Content-Length'] = Buffer.byteLength(data);
    }

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: parsedBody
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', reject);
    
    if (data && method !== 'GET') {
      req.write(data);
    }
    
    req.end();
  });
}

function createTestUpdate(messageText, userId = 123456789, updateId = null) {
  return {
    update_id: updateId || Date.now(),
    message: {
      message_id: Math.floor(Math.random() * 1000000),
      from: {
        id: userId,
        is_bot: false,
        first_name: "TestUser",
        username: "testuser",
        language_code: "en"
      },
      chat: {
        id: userId,
        first_name: "TestUser",
        username: "testuser",
        type: "private"
      },
      date: Math.floor(Date.now() / 1000),
      text: messageText
    }
  };
}

async function sendTestMessage(messageText, userId = 123456789) {
  const update = createTestUpdate(messageText, userId);
  
  try {
    const response = await makeRequest(
      WEBHOOK_URL,
      'POST',
      JSON.stringify(update),
      {
        'X-Telegram-Bot-Api-Secret-Token': SECRET_TOKEN
      }
    );

    if (response.status === 200 && response.data && response.data.ok) {
      log('green', `✅ Message "${messageText}" processed successfully`);
      return true;
    } else {
      log('red', `❌ Message "${messageText}" failed: ${response.status}`);
      if (response.data) {
        log('red', `   Error: ${JSON.stringify(response.data)}`);
      }
      return false;
    }
  } catch (error) {
    log('red', `❌ Error sending message "${messageText}": ${error.message}`);
    return false;
  }
}

async function testBasicCommands() {
  log('blue', '\n🎯 Testing Basic Commands...');
  
  const commands = [
    '/start',
    '/help',
    '/settings',
    '/status'
  ];

  const results = [];
  
  for (const command of commands) {
    log('cyan', `   Testing: ${command}`);
    const success = await sendTestMessage(command);
    results.push({ command, success });
    
    // Small delay between commands
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  const passed = results.filter(r => r.success).length;
  log('blue', `   Commands test: ${passed}/${commands.length} passed`);
  
  return passed === commands.length;
}

async function testConversation() {
  log('blue', '\n💬 Testing Conversation...');
  
  const messages = [
    'Hello!',
    'How are you?',
    'What can you do?',
    'Tell me about crypto',
    'What is Bitcoin?'
  ];

  const results = [];
  
  for (const message of messages) {
    log('cyan', `   Testing: "${message}"`);
    const success = await sendTestMessage(message);
    results.push({ message, success });
    
    // Small delay between messages
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  const passed = results.filter(r => r.success).length;
  log('blue', `   Conversation test: ${passed}/${messages.length} passed`);
  
  return passed === messages.length;
}

async function testTwitterIntegration() {
  log('blue', '\n🐦 Testing Twitter Integration...');
  
  const twitterMessages = [
    'https://twitter.com/elonmusk/status/1234567890',
    'https://x.com/bitcoin/status/9876543210',
    'Check this tweet: https://twitter.com/test/status/123'
  ];

  const results = [];
  
  for (const message of twitterMessages) {
    log('cyan', `   Testing: "${message}"`);
    const success = await sendTestMessage(message);
    results.push({ message, success });
    
    // Small delay between messages
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  const passed = results.filter(r => r.success).length;
  log('blue', `   Twitter integration test: ${passed}/${twitterMessages.length} passed`);
  
  return passed === twitterMessages.length;
}

async function testMultipleUsers() {
  log('blue', '\n👥 Testing Multiple Users...');
  
  const users = [
    { id: 111111111, name: 'User1' },
    { id: 222222222, name: 'User2' },
    { id: 333333333, name: 'User3' }
  ];

  const results = [];
  
  for (const user of users) {
    log('cyan', `   Testing user: ${user.name} (${user.id})`);
    const success = await sendTestMessage('/start', user.id);
    results.push({ user: user.name, success });
    
    // Small delay between users
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  const passed = results.filter(r => r.success).length;
  log('blue', `   Multiple users test: ${passed}/${users.length} passed`);
  
  return passed === users.length;
}

async function testErrorHandling() {
  log('blue', '\n🚨 Testing Error Handling...');
  
  const errorTests = [
    { name: 'Very long message', message: 'A'.repeat(5000) },
    { name: 'Special characters', message: '🚀💎🔥⚡🌟💰🎯🎉' },
    { name: 'Empty message', message: '' },
    { name: 'Only spaces', message: '   ' }
  ];

  const results = [];
  
  for (const test of errorTests) {
    log('cyan', `   Testing: ${test.name}`);
    const success = await sendTestMessage(test.message);
    results.push({ test: test.name, success });
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  const passed = results.filter(r => r.success).length;
  log('blue', `   Error handling test: ${passed}/${errorTests.length} passed`);
  
  return passed >= errorTests.length * 0.75; // Allow some failures for edge cases
}

async function runInteractionTests() {
  log('magenta', '╔════════════════════════════════════════╗');
  log('magenta', '║    TELEGRAM BOT INTERACTION TEST      ║');
  log('magenta', '╚════════════════════════════════════════╝');

  const testResults = {
    basicCommands: false,
    conversation: false,
    twitterIntegration: false,
    multipleUsers: false,
    errorHandling: false
  };

  // Run all interaction tests
  testResults.basicCommands = await testBasicCommands();
  testResults.conversation = await testConversation();
  testResults.twitterIntegration = await testTwitterIntegration();
  testResults.multipleUsers = await testMultipleUsers();
  testResults.errorHandling = await testErrorHandling();

  // Summary
  log('blue', '\n📋 INTERACTION TEST SUMMARY');
  log('blue', '================================');
  
  const passed = Object.values(testResults).filter(Boolean).length;
  const total = Object.keys(testResults).length;
  
  Object.entries(testResults).forEach(([test, passed]) => {
    const status = passed ? '✅' : '❌';
    const color = passed ? 'green' : 'red';
    log(color, `${status} ${test.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
  });

  log('blue', `\nOverall: ${passed}/${total} interaction tests passed`);
  
  if (passed >= total * 0.8) {
    log('green', '\n🎉 Bot interaction tests mostly passed!');
    log('green', '\nYour Telegram bot is ready for real users.');
    log('cyan', '\nBot details:');
    log('cyan', '• Username: @Benji_BuddyChip_Bot');
    log('cyan', '• Webhook: https://www.buddychip.app/api/telegram/webhook');
    log('cyan', '• Features: AI conversation, Twitter integration, crypto insights');
  } else {
    log('yellow', '\n⚠️  Some interaction tests failed. Check the logs above.');
  }

  return passed >= total * 0.8;
}

// Run the interaction tests
runInteractionTests().catch(console.error);

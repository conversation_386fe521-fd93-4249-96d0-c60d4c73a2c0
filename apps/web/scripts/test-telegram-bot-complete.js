#!/usr/bin/env node

/**
 * Complete Telegram Bot Test Suite
 * 
 * Tests all aspects of the Telegram bot functionality
 */

const https = require('https');

const BOT_TOKEN = '7652990262:AAExhfGhKMctbkPSIAZSLBu-JAIzyNBBq5M';
const TELEGRAM_API_BASE = `https://api.telegram.org/bot${BOT_TOKEN}`;
const WEBHOOK_URL = 'https://www.buddychip.app/api/telegram/webhook';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function log(color, message, prefix = '') {
  console.log(`${colors[color]}${prefix}${message}${colors.reset}`);
}

function makeRequest(url, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'User-Agent': 'BuddyChip-Bot-Test/1.0',
        ...headers
      }
    };

    if (data && method !== 'GET') {
      options.headers['Content-Type'] = 'application/json';
      options.headers['Content-Length'] = Buffer.byteLength(data);
    }

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: parsedBody
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', reject);
    
    if (data && method !== 'GET') {
      req.write(data);
    }
    
    req.end();
  });
}

async function testBotInfo() {
  log('blue', '\n🤖 Testing Bot Information...');
  
  try {
    const response = await makeRequest(`${TELEGRAM_API_BASE}/getMe`);
    
    if (response.data.ok) {
      const bot = response.data.result;
      log('green', '✅ Bot information retrieved successfully');
      log('cyan', `   Bot ID: ${bot.id}`);
      log('cyan', `   Bot Name: ${bot.first_name}`);
      log('cyan', `   Username: @${bot.username}`);
      log('cyan', `   Can Join Groups: ${bot.can_join_groups}`);
      return true;
    } else {
      log('red', `❌ Failed to get bot info: ${response.data.description}`);
      return false;
    }
  } catch (error) {
    log('red', `❌ Error getting bot info: ${error.message}`);
    return false;
  }
}

async function testWebhookStatus() {
  log('blue', '\n🔗 Testing Webhook Status...');
  
  try {
    const response = await makeRequest(`${TELEGRAM_API_BASE}/getWebhookInfo`);
    
    if (response.data.ok) {
      const webhook = response.data.result;
      log('green', '✅ Webhook information retrieved');
      log('cyan', `   URL: ${webhook.url || 'Not set'}`);
      log('cyan', `   Pending Updates: ${webhook.pending_update_count}`);
      log('cyan', `   Max Connections: ${webhook.max_connections || 'Default'}`);
      log('cyan', `   IP Address: ${webhook.ip_address || 'Not available'}`);
      
      if (webhook.url === WEBHOOK_URL) {
        log('green', '✅ Webhook URL is correctly configured');
        return true;
      } else {
        log('yellow', '⚠️  Webhook URL mismatch');
        return false;
      }
    } else {
      log('red', `❌ Failed to get webhook info: ${response.data.description}`);
      return false;
    }
  } catch (error) {
    log('red', `❌ Error getting webhook info: ${error.message}`);
    return false;
  }
}

async function testWebhookEndpoint() {
  log('blue', '\n🌐 Testing Webhook Endpoint...');
  
  try {
    // Test health check
    const healthResponse = await makeRequest(`${WEBHOOK_URL}?action=health`);
    
    if (healthResponse.status === 200) {
      log('green', '✅ Webhook health check passed');
      log('cyan', `   Status: ${healthResponse.data.status}`);
      log('cyan', `   Bot Configured: ${healthResponse.data.botConfigured}`);
    } else {
      log('red', `❌ Webhook health check failed: ${healthResponse.status}`);
      return false;
    }

    // Test webhook POST simulation
    const testUpdate = JSON.stringify({
      update_id: Date.now(),
      message: {
        message_id: 1,
        from: {
          id: 123456789,
          is_bot: false,
          first_name: "Test",
          username: "testuser"
        },
        chat: {
          id: 123456789,
          first_name: "Test",
          username: "testuser",
          type: "private"
        },
        date: Math.floor(Date.now() / 1000),
        text: "/start"
      }
    });

    const webhookResponse = await makeRequest(
      WEBHOOK_URL,
      'POST',
      testUpdate,
      {
        'X-Telegram-Bot-Api-Secret-Token': 'fedb2719f4a14793c848016d95e7378dcd7fab158f6ac7cd46ea266d645989d9'
      }
    );

    if (webhookResponse.status === 200 && webhookResponse.data.ok) {
      log('green', '✅ Webhook POST simulation successful');
      return true;
    } else {
      log('red', `❌ Webhook POST simulation failed: ${webhookResponse.status}`);
      return false;
    }

  } catch (error) {
    log('red', `❌ Error testing webhook endpoint: ${error.message}`);
    return false;
  }
}

async function testBotCommands() {
  log('blue', '\n⚡ Testing Bot Commands...');
  
  try {
    const response = await makeRequest(`${TELEGRAM_API_BASE}/getMyCommands`);
    
    if (response.data.ok) {
      const commands = response.data.result;
      log('green', '✅ Bot commands retrieved');
      
      if (commands.length > 0) {
        log('cyan', '   Available commands:');
        commands.forEach(cmd => {
          log('cyan', `     /${cmd.command} - ${cmd.description}`);
        });
      } else {
        log('yellow', '⚠️  No commands configured');
      }
      return true;
    } else {
      log('red', `❌ Failed to get commands: ${response.data.description}`);
      return false;
    }
  } catch (error) {
    log('red', `❌ Error getting commands: ${error.message}`);
    return false;
  }
}

async function runCompleteTest() {
  log('magenta', '╔════════════════════════════════════════╗');
  log('magenta', '║     COMPLETE TELEGRAM BOT TEST        ║');
  log('magenta', '╚════════════════════════════════════════╝');

  const results = {
    botInfo: false,
    webhookStatus: false,
    webhookEndpoint: false,
    botCommands: false
  };

  // Run all tests
  results.botInfo = await testBotInfo();
  results.webhookStatus = await testWebhookStatus();
  results.webhookEndpoint = await testWebhookEndpoint();
  results.botCommands = await testBotCommands();

  // Summary
  log('blue', '\n📋 TEST SUMMARY');
  log('blue', '================================');
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅' : '❌';
    const color = passed ? 'green' : 'red';
    log(color, `${status} ${test.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
  });

  log('blue', `\nOverall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    log('green', '\n🎉 All tests passed! Your Telegram bot is working correctly.');
    log('green', '\nNext steps:');
    log('cyan', '1. Start a conversation with @Benji_BuddyChip_Bot on Telegram');
    log('cyan', '2. Send /start to begin');
    log('cyan', '3. Test various commands and features');
  } else {
    log('red', '\n⚠️  Some tests failed. Please check the configuration.');
  }

  return passed === total;
}

// Run the complete test
runCompleteTest().catch(console.error);

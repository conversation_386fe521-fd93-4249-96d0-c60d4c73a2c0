
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.10.0
 * Query Engine version: aee10d5a411e4360c6d3445ce4810ca65adbf3e8
 */
Prisma.prismaVersion = {
  client: "6.10.0",
  engine: "aee10d5a411e4360c6d3445ce4810ca65adbf3e8"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  name: 'name',
  avatar: 'avatar',
  isAdmin: 'isAdmin',
  planId: 'planId',
  clerkPlanId: 'clerkPlanId',
  clerkPlanName: 'clerkPlanName',
  subscriptionStatus: 'subscriptionStatus',
  subscriptionUpdatedAt: 'subscriptionUpdatedAt',
  personalityId: 'personalityId',
  customSystemPrompt: 'customSystemPrompt',
  useFirstPerson: 'useFirstPerson',
  modelId: 'modelId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastActiveAt: 'lastActiveAt'
};

exports.Prisma.PersonalityProfileScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  systemPrompt: 'systemPrompt',
  isDefault: 'isDefault',
  isActive: 'isActive',
  isUserGenerated: 'isUserGenerated',
  sourceTwitterHandle: 'sourceTwitterHandle',
  generationMetadata: 'generationMetadata',
  createdById: 'createdById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PersonaGenerationJobScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  twitterHandle: 'twitterHandle',
  status: 'status',
  progress: 'progress',
  tweetsCollected: 'tweetsCollected',
  repliesCollected: 'repliesCollected',
  totalTweetsTarget: 'totalTweetsTarget',
  totalRepliesTarget: 'totalRepliesTarget',
  memoriesStored: 'memoriesStored',
  memoryStorageProgress: 'memoryStorageProgress',
  memoryErrors: 'memoryErrors',
  errorMessage: 'errorMessage',
  resultPersonalityId: 'resultPersonalityId',
  processingMetadata: 'processingMetadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  completedAt: 'completedAt'
};

exports.Prisma.AIModelScalarFieldEnum = {
  id: 'id',
  name: 'name',
  displayName: 'displayName',
  description: 'description',
  provider: 'provider',
  modelId: 'modelId',
  costTier: 'costTier',
  speed: 'speed',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubscriptionPlanScalarFieldEnum = {
  id: 'id',
  name: 'name',
  displayName: 'displayName',
  description: 'description',
  price: 'price',
  baseUsers: 'baseUsers',
  additionalUserPrice: 'additionalUserPrice',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PlanFeatureScalarFieldEnum = {
  id: 'id',
  planId: 'planId',
  feature: 'feature',
  limit: 'limit',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MonitoredAccountScalarFieldEnum = {
  id: 'id',
  twitterHandle: 'twitterHandle',
  twitterId: 'twitterId',
  displayName: 'displayName',
  avatarUrl: 'avatarUrl',
  sector: 'sector',
  isActive: 'isActive',
  userId: 'userId',
  syncMentions: 'syncMentions',
  syncUserTweets: 'syncUserTweets',
  syncReplies: 'syncReplies',
  syncRetweets: 'syncRetweets',
  lastCheckedAt: 'lastCheckedAt',
  totalMentions: 'totalMentions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MentionScalarFieldEnum = {
  id: 'id',
  content: 'content',
  link: 'link',
  authorName: 'authorName',
  authorHandle: 'authorHandle',
  authorId: 'authorId',
  authorAvatarUrl: 'authorAvatarUrl',
  authorVerified: 'authorVerified',
  mentionedAt: 'mentionedAt',
  replyCount: 'replyCount',
  retweetCount: 'retweetCount',
  likeCount: 'likeCount',
  isReply: 'isReply',
  parentTweetId: 'parentTweetId',
  bullishScore: 'bullishScore',
  importanceScore: 'importanceScore',
  analysisData: 'analysisData',
  keywords: 'keywords',
  accountId: 'accountId',
  userId: 'userId',
  processed: 'processed',
  processingError: 'processingError',
  archived: 'archived',
  archivedAt: 'archivedAt',
  isUserTweet: 'isUserTweet',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AIResponseScalarFieldEnum = {
  id: 'id',
  content: 'content',
  model: 'model',
  prompt: 'prompt',
  tokensUsed: 'tokensUsed',
  mentionId: 'mentionId',
  userId: 'userId',
  confidence: 'confidence',
  rating: 'rating',
  used: 'used',
  processingTime: 'processingTime',
  version: 'version',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ImageScalarFieldEnum = {
  id: 'id',
  url: 'url',
  filename: 'filename',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  uploadKey: 'uploadKey',
  width: 'width',
  height: 'height',
  altText: 'altText',
  aiResponseId: 'aiResponseId',
  mentionId: 'mentionId',
  userId: 'userId',
  processed: 'processed',
  isPublic: 'isPublic',
  uploadedAt: 'uploadedAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UsageLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  feature: 'feature',
  amount: 'amount',
  metadata: 'metadata',
  billingPeriod: 'billingPeriod',
  createdAt: 'createdAt'
};

exports.Prisma.MentionNotepadScalarFieldEnum = {
  id: 'id',
  mentionId: 'mentionId',
  userId: 'userId',
  title: 'title',
  notes: 'notes',
  draftResponse: 'draftResponse',
  finalResponse: 'finalResponse',
  researchQuery: 'researchQuery',
  researchContext: 'researchContext',
  isActive: 'isActive',
  lastUsedAt: 'lastUsedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotepadSourceScalarFieldEnum = {
  id: 'id',
  notepadId: 'notepadId',
  title: 'title',
  url: 'url',
  content: 'content',
  sourceType: 'sourceType',
  searchTool: 'searchTool',
  relevanceScore: 'relevanceScore',
  credibilityScore: 'credibilityScore',
  publishedAt: 'publishedAt',
  extractedAt: 'extractedAt',
  isBookmarked: 'isBookmarked',
  userRating: 'userRating',
  userNotes: 'userNotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotepadDraftScalarFieldEnum = {
  id: 'id',
  notepadId: 'notepadId',
  content: 'content',
  version: 'version',
  title: 'title',
  generatedBy: 'generatedBy',
  model: 'model',
  prompt: 'prompt',
  tokensUsed: 'tokensUsed',
  isActive: 'isActive',
  isFavorite: 'isFavorite',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CryptoSectorsCacheScalarFieldEnum = {
  id: 'id',
  data: 'data',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CryptoTrendingCacheScalarFieldEnum = {
  id: 'id',
  sectorSlug: 'sectorSlug',
  timeframe: 'timeframe',
  data: 'data',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MemoryScalarFieldEnum = {
  id: 'id',
  embedding: 'embedding',
  content: 'content',
  metadata: 'metadata',
  userId: 'userId',
  memoryType: 'memoryType',
  relevanceScore: 'relevanceScore',
  accessCount: 'accessCount',
  lastAccessedAt: 'lastAccessedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TelegramUserScalarFieldEnum = {
  id: 'id',
  telegramId: 'telegramId',
  username: 'username',
  firstName: 'firstName',
  lastName: 'lastName',
  languageCode: 'languageCode',
  userId: 'userId',
  isActive: 'isActive',
  isBlocked: 'isBlocked',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastActiveAt: 'lastActiveAt'
};

exports.Prisma.TelegramSessionScalarFieldEnum = {
  id: 'id',
  telegramUserId: 'telegramUserId',
  context: 'context',
  currentTool: 'currentTool',
  isActive: 'isActive',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SyncJobScalarFieldEnum = {
  id: 'id',
  type: 'type',
  payload: 'payload',
  status: 'status',
  attempts: 'attempts',
  maxAttempts: 'maxAttempts',
  error: 'error',
  lastError: 'lastError',
  scheduledAt: 'scheduledAt',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  priority: 'priority',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.FeatureType = exports.$Enums.FeatureType = {
  AI_CALLS: 'AI_CALLS',
  IMAGE_GENERATIONS: 'IMAGE_GENERATIONS',
  MONITORED_ACCOUNTS: 'MONITORED_ACCOUNTS',
  MENTIONS_PER_MONTH: 'MENTIONS_PER_MONTH',
  MENTIONS_PER_SYNC: 'MENTIONS_PER_SYNC',
  MAX_TOTAL_MENTIONS: 'MAX_TOTAL_MENTIONS',
  STORAGE_GB: 'STORAGE_GB',
  TEAM_MEMBERS: 'TEAM_MEMBERS',
  COOKIE_API_CALLS: 'COOKIE_API_CALLS',
  PERSONA_GENERATIONS: 'PERSONA_GENERATIONS',
  PERSONA_MEMORY_OPS: 'PERSONA_MEMORY_OPS'
};

exports.Prisma.ModelName = {
  User: 'User',
  PersonalityProfile: 'PersonalityProfile',
  PersonaGenerationJob: 'PersonaGenerationJob',
  AIModel: 'AIModel',
  SubscriptionPlan: 'SubscriptionPlan',
  PlanFeature: 'PlanFeature',
  MonitoredAccount: 'MonitoredAccount',
  Mention: 'Mention',
  AIResponse: 'AIResponse',
  Image: 'Image',
  UsageLog: 'UsageLog',
  MentionNotepad: 'MentionNotepad',
  NotepadSource: 'NotepadSource',
  NotepadDraft: 'NotepadDraft',
  CryptoSectorsCache: 'CryptoSectorsCache',
  CryptoTrendingCache: 'CryptoTrendingCache',
  Memory: 'Memory',
  TelegramUser: 'TelegramUser',
  TelegramSession: 'TelegramSession',
  SyncJob: 'SyncJob'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)

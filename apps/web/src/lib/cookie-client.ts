/**
 * Cookie.fun API Client for BuddyChip
 *
 * Integrates with <PERSON><PERSON>.fun ProjectsV3 API for crypto project analytics,
 * mindshare metrics, and social media intelligence
 */

import { z } from "zod";

// API Configuration
const COOKIE_API_BASE_URL = "https://api.staging.cookie.fun";
const COOKIE_API_KEY = process.env.COOKIE_API_KEY;

// Flag to enable/disable fallback mode
// Set to false when Cookie.fun API is working
const USE_FALLBACK_DATA = false; // API is now working with staging URL!

// Cookie.fun API client - using real data only

// Response schemas for type safety
const SectorSchema = z.object({
  id: z.string(),
  name: z.string(),
  slug: z.string(),
  description: z.string(),
});

const SmartFollowerSchema = z.object({
  id: z.string(),
  username: z.string(),
  displayName: z.string(),
  profileImageUrl: z.string().optional(),
  followerCount: z.number().optional(),
  followingCount: z.number().optional(),
  isVerified: z.boolean().optional(),
  smartScore: z.number().optional(),
  influence: z.number().optional(),
  sector: z.string().optional(),
});

const TweetMetricsSchema = z.object({
  impressions: z.number().optional(),
  engagements: z.number().optional(),
  retweets: z.number().optional(),
  likes: z.number().optional(),
  replies: z.number().optional(),
  quotes: z.number().optional(),
});

const AccountFeedTweetSchema = z.object({
  id: z.string(),
  text: z.string(),
  createdAt: z.string(),
  type: z.enum(["Original", "Reply", "Quote"]).optional(),
  hasMedia: z.boolean().optional(),
  metrics: TweetMetricsSchema.optional(),
  author: z.object({
    username: z.string(),
    displayName: z.string(),
    profileImageUrl: z.string().optional(),
  }),
});

const ProjectSchema = z.object({
  id: z.string(),
  name: z.string(),
  slug: z.string(),
  symbol: z.string().optional(),
  description: z.string().optional(),
  sector: z.string().optional(),
  mindshare: z.number().optional(),
  mindshareDelta: z.number().optional(),
  smartEngagementPoints: z.number().optional(),
  matchingTweetsCount: z.number().optional(),
  trending: z.boolean().optional(),
  websiteUrl: z.string().optional(),
  twitterUrl: z.string().optional(),
});

const TimeseriesDataPointSchema = z.object({
  timestamp: z.string(),
  value: z.number(),
  impressions: z.number().optional(),
  engagementRate: z.number().optional(),
  mentions: z.number().optional(),
});

// API Response schemas
const SectorsResponseSchema = z.object({
  data: z.array(SectorSchema),
  success: z.boolean(),
  message: z.string().optional(),
});

const SmartFollowersResponseSchema = z
  .object({
    data: z.array(SmartFollowerSchema).optional(),
    success: z.boolean().optional(),
    message: z.string().optional(),
    pagination: z
      .object({
        hasMore: z.boolean(),
        nextCursor: z.string().optional(),
      })
      .optional(),
  })
  .passthrough();

const AccountFeedResponseSchema = z.object({
  data: z.array(AccountFeedTweetSchema),
  success: z.boolean(),
  message: z.string().optional(),
  pagination: z
    .object({
      hasMore: z.boolean(),
      nextCursor: z.string().optional(),
    })
    .optional(),
});

const ProjectSearchResponseSchema = z.object({
  data: z.array(ProjectSchema),
  success: z.boolean(),
  message: z.string().optional(),
  pagination: z
    .object({
      hasMore: z.boolean(),
      nextCursor: z.string().optional(),
    })
    .optional(),
  timeseries: z.array(TimeseriesDataPointSchema).optional(),
});

// Type exports
export type Sector = z.infer<typeof SectorSchema>;
export type SmartFollower = z.infer<typeof SmartFollowerSchema>;
export type AccountFeedTweet = z.infer<typeof AccountFeedTweetSchema>;
export type Project = z.infer<typeof ProjectSchema>;
export type TimeseriesDataPoint = z.infer<typeof TimeseriesDataPointSchema>;
export type SectorsResponse = z.infer<typeof SectorsResponseSchema>;
export type SmartFollowersResponse = z.infer<
  typeof SmartFollowersResponseSchema
>;
export type AccountFeedResponse = z.infer<typeof AccountFeedResponseSchema>;
export type ProjectSearchResponse = z.infer<typeof ProjectSearchResponseSchema>;

// Enhanced in-memory cache with different TTLs per endpoint type
const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

// Different TTL strategies based on data volatility
const CACHE_TTLS = {
  SECTORS: 60 * 60 * 1000, // 1 hour - sectors rarely change
  TRENDING: 5 * 60 * 1000, // 5 minutes - trending data is dynamic
  SMART_FOLLOWERS: 15 * 60 * 1000, // 15 minutes - follower data is moderately stable
  PROJECT_SEARCH: 10 * 60 * 1000, // 10 minutes - project data is fairly stable
  ACCOUNT_FEED: 2 * 60 * 1000, // 2 minutes - feed data changes frequently
  METRICS: 5 * 60 * 1000, // 5 minutes - metrics change regularly
} as const;

class CookieAPIClient {
  private requestCount: number = 0;
  private lastResetTime: number = Date.now();

  /**
   * Determine appropriate cache TTL based on endpoint
   */
  private getCacheTTL(endpoint: string): number {
    if (endpoint.includes("/sectors")) return CACHE_TTLS.SECTORS;
    if (endpoint.includes("/trending") || endpoint.includes("mindshare"))
      return CACHE_TTLS.TRENDING;
    if (endpoint.includes("/smart-followers"))
      return CACHE_TTLS.SMART_FOLLOWERS;
    if (endpoint.includes("/search") || endpoint.includes("/projects"))
      return CACHE_TTLS.PROJECT_SEARCH;
    if (endpoint.includes("/feed") || endpoint.includes("/tweets"))
      return CACHE_TTLS.ACCOUNT_FEED;
    if (endpoint.includes("/metrics") || endpoint.includes("/analytics"))
      return CACHE_TTLS.METRICS;

    // Default TTL for unknown endpoints
    return CACHE_TTLS.PROJECT_SEARCH;
  }
  private readonly MAX_REQUESTS_PER_MINUTE = 60; // Internal rate limiting

  /**
   * Internal rate limiting to protect against API abuse
   */
  private checkInternalRateLimit(): boolean {
    const now = Date.now();
    const timeSinceReset = now - this.lastResetTime;

    // Reset counter every minute
    if (timeSinceReset >= 60000) {
      this.requestCount = 0;
      this.lastResetTime = now;
    }

    if (this.requestCount >= this.MAX_REQUESTS_PER_MINUTE) {
      console.warn(
        "🚨 Cookie.fun API internal rate limit exceeded - blocking request"
      );
      return false;
    }

    this.requestCount++;
    return true;
  }

  /**
   * Validate and sanitize endpoint to prevent injection attacks
   */
  private validateEndpoint(endpoint: string): boolean {
    // Only allow specific patterns for Cookie.fun API endpoints
    const allowedPatterns = [
      /^\/v3\/sectors$/,
      /^\/v3\/account$/,
      /^\/v3\/project$/,
      /^\/v3\/account\/smart-followers$/,
      /^\/v3\/project\/search$/,
      /^\/v3\/project\/mindshare-leaderboard$/,
    ];

    return allowedPatterns.some((pattern) => pattern.test(endpoint));
  }

  /**
   * Sanitize request body to prevent injection attacks
   */
  private sanitizeBody(body: any): any {
    if (!body || typeof body !== "object") return body;

    const sanitized: any = {};
    const allowedKeys = [
      "username",
      "userId",
      "slug",
      "contractAddress",
      "query",
      "page",
      "limit",
      "mindshareTimeframe",
      "sortBy",
      "sortOrder",
      "sectorSlug",
      "projectSlug",
      "searchQuery",
    ];

    for (const key of allowedKeys) {
      if (key in body) {
        const value = body[key];
        // Basic sanitization - remove potential injection characters
        if (typeof value === "string") {
          sanitized[key] = value
            .replace(/[<>'"&]/g, "")
            .trim()
            .substring(0, 100);
        } else if (typeof value === "number" && value >= 0 && value <= 10000) {
          sanitized[key] = value;
        } else if (typeof value === "boolean") {
          sanitized[key] = value;
        }
      }
    }

    return sanitized;
  }

  private async makeRequest(
    endpoint: string,
    method: "GET" | "POST" = "GET",
    body?: any
  ): Promise<any> {
    // Check API key first, before any caching
    if (!COOKIE_API_KEY) {
      throw new Error(
        "COOKIE_API_KEY environment variable is required for Cookie.fun API operations"
      );
    }

    // Validate endpoint to prevent injection attacks
    if (!this.validateEndpoint(endpoint)) {
      throw new Error(
        `Invalid endpoint: ${endpoint}. Only whitelisted Cookie.fun API endpoints are allowed.`
      );
    }

    // Sanitize request body
    if (body) {
      body = this.sanitizeBody(body);
    }

    // Check internal rate limiting
    if (!this.checkInternalRateLimit()) {
      throw new Error(
        "Internal rate limit exceeded. Please wait before making more requests."
      );
    }

    const url = `${COOKIE_API_BASE_URL}${endpoint}`;

    // Create cache key including method and body for POST requests
    const cacheKey = `${method}:${url}:${body ? JSON.stringify(body) : ""}`;
    const cached = cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      const remainingTime = Math.ceil(
        (cached.ttl - (Date.now() - cached.timestamp)) / 1000
      );
      console.log(
        `✅ Cache hit for Cookie.fun ${endpoint} (${remainingTime}s remaining)`
      );
      return cached.data;
    }

    try {
      console.log(`🍪 Making Cookie.fun API request: ${method} ${endpoint}`);
      console.log(
        `🔒 Request count: ${this.requestCount}/${this.MAX_REQUESTS_PER_MINUTE} this minute`
      );

      const requestOptions: RequestInit = {
        method,
        headers: {
          "X-API-Key": COOKIE_API_KEY,
          "Content-Type": "application/json",
        },
      };

      if (method === "POST" && body) {
        requestOptions.body = JSON.stringify(body);
      }

      const response = await fetch(url, requestOptions);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `❌ Cookie.fun API ${response.status} error for ${endpoint}:`,
          errorText
        );

        // Provide more specific error messages
        if (response.status === 404) {
          throw new Error(
            `Cookie.fun API endpoint not found (404): ${endpoint}. This endpoint may not exist or may have been deprecated.`
          );
        } else if (response.status === 401) {
          // Parse the error response to get more specific error information
          let errorMessage =
            "Cookie.fun API authentication failed (401). Please check your COOKIE_API_KEY.";
          try {
            const errorData = JSON.parse(errorText);
            if (errorData.error && errorData.error.errorMessage) {
              if (errorData.error.errorMessage.includes("expired")) {
                errorMessage =
                  "Cookie.fun API key has expired. Please contact support to renew the API access.";
              } else {
                errorMessage = `Cookie.fun API authentication failed: ${errorData.error.errorMessage}`;
              }
            }
          } catch (parseError) {
            // Keep default message if we can't parse the error
          }
          throw new Error(errorMessage);
        } else if (response.status === 403) {
          throw new Error(
            `Cookie.fun API access forbidden (403). Your API key may not have permission for this endpoint.`
          );
        } else if (response.status === 429) {
          throw new Error(
            `Cookie.fun API rate limit exceeded (429). Please try again later.`
          );
        }

        throw new Error(
          `Cookie.fun API error (${response.status}): ${errorText}`
        );
      }

      const data = await response.json();

      // Cache successful responses with endpoint-specific TTL
      const ttl = this.getCacheTTL(endpoint);
      cache.set(cacheKey, { data, timestamp: Date.now(), ttl });
      console.log(
        `💾 Cached Cookie.fun response for ${endpoint} (TTL: ${ttl / 1000}s)`
      );

      // Handle the actual Cookie.fun API response structure
      // Response format: { ok: {...}, success: true, error: null }
      if (data.success && data.ok) {
        console.log(`✅ Cookie.fun API success: ${endpoint}`);
        return data.ok; // Return the actual data from 'ok' field
      } else if (data.success && data.data) {
        // Fallback for different response formats
        console.log(
          `✅ Cookie.fun API success: ${endpoint} (${Array.isArray(data.data) ? data.data.length : "unknown"} items)`
        );
        return data;
      } else {
        console.log(`⚠️ Cookie.fun API unexpected response format: ${endpoint}`);
        console.log(`Response keys: ${Object.keys(data).join(", ")}`);
        return data;
      }
    } catch (error) {
      console.error(`❌ Cookie.fun API error for ${endpoint}:`, error);
      throw new Error(
        `Failed to fetch from Cookie.fun API: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Get account details
   * POST /v3/account
   */
  async getAccount(params: {
    username?: string;
    userId?: string;
  }): Promise<any> {
    if (!params.username && !params.userId) {
      throw new Error("Either username or userId must be provided");
    }

    const body = {
      ...(params.username && { username: params.username }),
      ...(params.userId && { userId: params.userId }),
    };

    const response = await this.makeRequest("/v3/account", "POST", body);
    return response;
  }

  /**
   * Get project details
   * POST /v3/project
   */
  async getProject(params: {
    slug?: string;
    contractAddress?: string;
  }): Promise<any> {
    if (!params.slug && !params.contractAddress) {
      throw new Error("Either slug or contractAddress must be provided");
    }

    const body = {
      ...(params.slug && { slug: params.slug }),
      ...(params.contractAddress && {
        contractAddress: params.contractAddress,
      }),
    };

    const response = await this.makeRequest("/v3/project", "POST", body);
    return response;
  }

  /**
   * Get all available crypto sectors
   * GET /v3/sectors
   */
  async getSectors(): Promise<SectorsResponse> {
    try {
      console.log("🍪 Getting sectors from Cookie.fun API: GET /v3/sectors");

      const response = await this.makeRequest("/v3/sectors", "GET");

      console.log(
        "🍪 Raw sectors response:",
        JSON.stringify(response, null, 2)
      );

      // Handle different response structures
      let sectorsData = [];

      if (Array.isArray(response)) {
        sectorsData = response;
      } else if (response && response.data && Array.isArray(response.data)) {
        sectorsData = response.data;
      } else if (response && typeof response === "object") {
        // If response is an object, it might contain sectors data
        console.log("🍪 Sectors response is object, checking for data...");
        sectorsData = [];
      }

      // If we have sectors data, use it
      if (sectorsData.length > 0) {
        console.log(`✅ Found ${sectorsData.length} sectors from API`);
        return SectorsResponseSchema.parse({
          success: true,
          data: sectorsData,
        });
      }

      // If no sectors data, create basic sectors for the UI with valid slugs
      console.log("🍪 No sectors data from API, creating basic sectors...");
      const basicSectors = [
        {
          id: "defi",
          name: "DeFi",
          slug: "defi",
          description: "Decentralized Finance",
        },
        {
          id: "infrastructure",
          name: "Infrastructure",
          slug: "infrastructure",
          description: "Blockchain Infrastructure",
        },
        {
          id: "gaming",
          name: "Gaming",
          slug: "gaming",
          description: "Blockchain Gaming",
        },
        {
          id: "ai",
          name: "AI",
          slug: "ai",
          description: "Artificial Intelligence",
        },
        { id: "meme", name: "Meme", slug: "meme", description: "Meme Coins" },
      ];

      return {
        success: true,
        data: basicSectors,
      };
    } catch (error) {
      console.error("🍪 Sectors endpoint failed:", error);
      throw new Error("Failed to fetch sectors from Cookie.fun API");
    }
  }

  /**
   * Get smart followers for a Twitter account
   * POST /v3/account/smart-followers
   */
  async getSmartFollowers(params: {
    username?: string;
    userId?: string;
    limit?: number;
  }): Promise<SmartFollowersResponse> {
    if (!params.username && !params.userId) {
      throw new Error("Either username or userId must be provided");
    }

    const body = {
      ...(params.username && { username: params.username.replace("@", "") }),
      ...(params.userId && { userId: params.userId }),
      ...(params.limit && { limit: params.limit }),
    };

    try {
      const response = await this.makeRequest(
        "/v3/account/smart-followers",
        "POST",
        body
      );

      // Transform the actual Cookie.fun API response structure
      // Response format: { entries: [...], totalCount: number, currentPage: number, totalPages: number }
      let followers: SmartFollower[] = [];

      if (response && response.entries && Array.isArray(response.entries)) {
        followers = response.entries.map((entry: any) => ({
          id: entry.twitterId || entry.twitterUsername, // Use twitterId as unique identifier
          username: entry.twitterUsername,
          displayName: entry.displayName,
          profileImageUrl: entry.profileImageUrl,
          followerCount: entry.followersCount,
          followingCount: entry.followingCount,
          isVerified: entry.verified || false,
          smartScore: entry.metrics?.last7Days?.engagementsCount || 0,
          influence: entry.metrics?.last7Days?.impressionsCount || 0,
          sector: "unknown", // Cookie.fun doesn't provide sector in smart followers
          bio: entry.rawDescription,
        }));

        console.log(
          `✅ Transformed ${followers.length} smart followers from Cookie.fun API`
        );
      } else {
        console.log(
          "🍪 Smart followers response structure:",
          Object.keys(response || {})
        );
      }

      return SmartFollowersResponseSchema.parse({
        success: true,
        data: followers,
        message: `Found ${followers.length} smart followers`,
      });
    } catch (error) {
      console.error("🍪 Smart followers error:", error);

      // Return a valid schema-compliant response for failures
      return SmartFollowersResponseSchema.parse({
        success: false,
        data: [],
        message: "Failed to fetch smart followers from Cookie.fun API",
      });
    }
  }

  /**
   * Get account feed with filtering options
   * POST /v3/account/feed
   */
  async getAccountFeed(params: {
    username?: string;
    userId?: string;
    startDate?: string;
    endDate?: string;
    type?: "Original" | "Reply" | "Quote";
    hasMedia?: boolean;
    sortBy?: "CreatedAt" | "Impressions";
    sortOrder?: "Ascending" | "Descending";
    limit?: number;
  }): Promise<AccountFeedResponse> {
    if (!params.username && !params.userId) {
      throw new Error("Either username or userId must be provided");
    }

    const body = {
      ...(params.username && { username: params.username.replace("@", "") }),
      ...(params.userId && { userId: params.userId }),
      ...(params.startDate && { startDate: params.startDate }),
      ...(params.endDate && { endDate: params.endDate }),
      ...(params.type && { type: params.type }),
      ...(params.hasMedia !== undefined && { hasMedia: params.hasMedia }),
      ...(params.sortBy && { sortBy: params.sortBy }),
      ...(params.sortOrder && { sortOrder: params.sortOrder }),
      ...(params.limit && { limit: Math.min(params.limit, 20) }), // API limit is 20
    };

    const response = await this.makeRequest("/v3/account/feed", "POST", body);
    return AccountFeedResponseSchema.parse(response);
  }

  /**
   * Search for crypto projects (Updated API)
   * POST /v3/project/search
   */
  async searchProjects(
    params: { query?: string; page?: number; limit?: number } = {}
  ): Promise<ProjectSearchResponse> {
    try {
      const body = {
        ...(params.query && { query: params.query }),
        ...(params.page && { page: params.page }),
        ...(params.limit && { limit: Math.min(params.limit, 20) }), // API limit is 20
      };

      const response = await this.makeRequest(
        "/v3/project/search",
        "POST",
        body
      );

      // Transform the response to match our expected format
      const transformedResponse = {
        success: true,
        data: response.data || response.projects || [],
        pagination: {
          page: params.page || 1,
          limit: params.limit || 20,
          total: response.total || 0,
          hasMore: false,
        },
      };

      return ProjectSearchResponseSchema.parse(transformedResponse);
    } catch (error) {
      console.error("🍪 Search projects API failed:", error);
      throw new Error("Failed to search projects from Cookie.fun API");
    }
  }

  /**
   * Get project mindshare leaderboard (Updated API)
   * POST /v3/project/mindshare-leaderboard
   */
  async getProjectMindshareLeaderboard(params: {
    mindshareTimeframe:
      | "_24Hours"
      | "_3Days"
      | "_7Days"
      | "_14Days"
      | "_30Days";
    sectorSlug?: string;
    sortBy?: "Mindshare" | "MindshareDelta";
    sortOrder?: "Ascending" | "Descending";
  }): Promise<ProjectSearchResponse> {
    try {
      const body = {
        mindshareTimeframe: params.mindshareTimeframe,
        ...(params.sectorSlug && { sectorSlug: params.sectorSlug }),
        ...(params.sortBy && { sortBy: params.sortBy }),
        ...(params.sortOrder && { sortOrder: params.sortOrder }),
      };

      const response = await this.makeRequest(
        "/v3/project/mindshare-leaderboard",
        "POST",
        body
      );

      // Transform the response to match our expected format
      // The actual response structure is: { ok: { entries: [...] }, success: true, error: null }
      const entries =
        response.ok?.entries || response.entries || response.data || [];

      // Transform entries to match our Project interface
      const projects = entries.map((entry: any) => ({
        id: entry.projectSlug || entry.projectId,
        name: entry.projectName,
        slug: entry.projectSlug,
        symbol: entry.projectSymbol || entry.projectName.toUpperCase(),
        sector: params.sectorSlug || "unknown",
        mindshare: Math.round((entry.mindshare || 0) * 100) / 100, // Keep decimal precision
        mindshareDelta: Math.round((entry.mindshareDelta || 0) * 100) / 100, // Include mindshare delta
        smartEngagementPoints: entry.smartEngagementPoints || 0,
        trending: true,
        twitterUrl: entry.twitterUrl,
        websiteUrl: entry.websiteUrl,
        description: entry.description || entry.projectName,
      }));

      const transformedResponse = {
        success: true,
        data: projects,
        pagination: {
          page: 1,
          limit: entries.length,
          total: entries.length,
          hasMore: false,
        },
      };

      return ProjectSearchResponseSchema.parse(transformedResponse);
    } catch (error) {
      console.error("🍪 Mindshare leaderboard API failed:", error);
      throw new Error(
        "Failed to fetch mindshare leaderboard from Cookie.fun API"
      );
    }
  }

  /**
   * Get mindshare data for projects in a sector (Legacy method)
   * Uses the mindshare leaderboard endpoint
   */
  async getMindshareData(
    sectorSlug?: string,
    timeframe: "_7Days" | "_30Days" = "_30Days"
  ): Promise<ProjectSearchResponse> {
    try {
      // Use the exact format from Cookie.fun documentation for mindshare queries
      const body: any = {
        mindshareTimeframe: timeframe,
        sortBy: "Mindshare",
        sortOrder: "Descending",
      };

      // Add sector filter if provided
      if (sectorSlug) {
        body.sectorSlug = sectorSlug;
      }

      const response = await this.makeRequest(
        "/v3/project/search",
        "POST",
        body
      );
      return ProjectSearchResponseSchema.parse(response);
    } catch (error) {
      console.error("🍪 Mindshare API failed:", error);
      throw new Error("Failed to fetch mindshare data from Cookie.fun API");
    }
  }

  /**
   * Get trending projects in a specific sector
   * Uses the mindshare leaderboard endpoint which returns the correct data structure
   */
  async getTrendingProjects(
    sectorSlug?: string,
    timeframe: "_7Days" | "_30Days" = "_7Days"
  ): Promise<Project[]> {
    try {
      console.log(
        `🍪 Getting trending projects for sector: ${sectorSlug || "all"}, timeframe: ${timeframe}`
      );

      // Use the mindshare leaderboard endpoint that returns the data structure you showed
      const body: any = {
        mindshareTimeframe: timeframe,
        sortBy: "Mindshare",
        sortOrder: "Descending",
      };

      // Add sector filter if provided
      if (sectorSlug && sectorSlug !== "all") {
        body.sectorSlug = sectorSlug;
      }

      console.log(
        `🍪 Making Cookie.fun API request: POST /v3/project/mindshare-leaderboard`
      );
      console.log(`🍪 Request body:`, JSON.stringify(body, null, 2));
      const response = await this.makeRequest(
        "/v3/project/mindshare-leaderboard",
        "POST",
        body
      );

      console.log(`🍪 Raw API response:`, JSON.stringify(response, null, 2));

      // Transform the response to match our Project interface
      // Based on your example: { ok: { entries: [...] }, success: true, error: null }
      const entries =
        response.ok?.entries || response.entries || response.data || [];
      const projects = entries.map((entry: any) => ({
        id: entry.projectId || entry.projectSlug,
        name: entry.projectName,
        slug: entry.projectSlug,
        symbol: entry.projectSymbol || entry.projectName?.toUpperCase(),
        sector: sectorSlug || entry.sector || "unknown",
        mindshare: Math.round((entry.mindshare || 0) * 100) / 100,
        mindshareDelta: Math.round((entry.mindshareDelta || 0) * 100) / 100,
        smartEngagementPoints: entry.smartEngagementPoints || 0,
        trending: true,
        twitterUrl: entry.twitterUrl,
        websiteUrl: entry.websiteUrl,
        description: entry.description || entry.projectName,
      }));

      console.log(
        `✅ Successfully fetched ${projects.length} trending projects`
      );
      return projects;
    } catch (error) {
      console.error("🍪 Trending projects API failed:", error);

      // If sector-specific request fails, try without sector filter
      if (
        sectorSlug &&
        error instanceof Error &&
        error.message.includes("Sector not found")
      ) {
        console.log(
          `⚠️ Sector "${sectorSlug}" not found, trying without sector filter...`
        );
        try {
          const fallbackBody = {
            mindshareTimeframe: timeframe,
            sortBy: "Mindshare",
            sortOrder: "Descending",
          };

          console.log(`🍪 Making fallback API request without sector filter`);
          const fallbackResponse = await this.makeRequest(
            "/v3/project/mindshare-leaderboard",
            "POST",
            fallbackBody
          );

          const fallbackEntries =
            fallbackResponse.ok?.entries ||
            fallbackResponse.entries ||
            fallbackResponse.data ||
            [];
          const fallbackProjects = fallbackEntries.map((entry: any) => ({
            id: entry.projectId || entry.projectSlug,
            name: entry.projectName,
            slug: entry.projectSlug,
            symbol: entry.projectSymbol || entry.projectName?.toUpperCase(),
            sector: entry.sector || "unknown",
            mindshare: Math.round((entry.mindshare || 0) * 100) / 100,
            mindshareDelta: Math.round((entry.mindshareDelta || 0) * 100) / 100,
            smartEngagementPoints: entry.smartEngagementPoints || 0,
            trending: true,
            twitterUrl: entry.twitterUrl,
            websiteUrl: entry.websiteUrl,
            description: entry.description || entry.projectName,
          }));

          console.log(
            `✅ Fallback: fetched ${fallbackProjects.length} trending projects (all sectors)`
          );
          return fallbackProjects;
        } catch (fallbackError) {
          console.error("🍪 Fallback also failed:", fallbackError);
        }
      }

      throw new Error("Failed to fetch trending projects from Cookie.fun API");
    }
  }

  /**
   * Get project metrics over time
   * Convenience method for timeseries data
   */
  async getProjectMetrics(params: {
    projectSlug: string;
    metricType: "Impressions" | "EngagementRate" | "Mentions";
    granulation: "_1Hour" | "_24Hours";
    startDate?: string;
    endDate?: string;
  }): Promise<TimeseriesDataPoint[]> {
    const response = await this.searchProjects({
      query: params.projectSlug,
      limit: 1,
    });

    return response.timeseries || [];
  }

  /**
   * Find smart followers in a specific sector
   * Convenience method combining smart followers with sector filtering
   */
  async findSectorInfluencers(
    username: string,
    targetSector: string
  ): Promise<SmartFollower[]> {
    const response = await this.getSmartFollowers({ username });

    // Filter by sector if available in follower data
    return (
      response.data?.filter((follower) =>
        follower.sector?.toLowerCase().includes(targetSector.toLowerCase())
      ) || []
    );
  }

  /**
   * Get competitive analysis for a project
   * Returns top projects in the same sector
   */
  async getCompetitiveAnalysis(projectSlug: string): Promise<{
    project: Project | null;
    competitors: Project[];
    sectorTrends: Project[];
  }> {
    // First get the project details to find its sector
    const projectResponse = await this.searchProjects({
      query: projectSlug,
      limit: 1,
    });
    const project = projectResponse.data[0] || null;

    if (!project || !project.sector) {
      return { project, competitors: [], sectorTrends: [] };
    }

    // Get top projects in the same sector
    const sectorResponse = await this.searchProjects({
      query: project.sector,
      limit: 20,
    });

    // Separate the target project from competitors
    const competitors = sectorResponse.data.filter(
      (p) => p.slug !== projectSlug
    );
    const sectorTrends = competitors.slice(0, 10); // Top 10 trending in sector

    return {
      project,
      competitors: competitors.slice(0, 5), // Top 5 competitors
      sectorTrends,
    };
  }

  /**
   * Test API connection and available endpoints
   */
  async testConnection(): Promise<{
    success: boolean;
    message: string;
    availableEndpoints?: string[];
  }> {
    if (!COOKIE_API_KEY) {
      return { success: false, message: "COOKIE_API_KEY not configured" };
    }

    try {
      // Try a simple endpoint first
      console.log("🍪 Testing Cookie.fun API connection...");

      // Test different possible endpoints
      const testEndpoints = [
        "/v3/sectors",
        "/v2/sectors",
        "/sectors",
        "/v3/projects",
        "/v2/projects",
        "/projects",
      ];

      const availableEndpoints: string[] = [];

      for (const endpoint of testEndpoints) {
        try {
          await this.makeRequest(endpoint);
          availableEndpoints.push(endpoint);
          console.log(`✅ Endpoint available: ${endpoint}`);
        } catch (error) {
          console.log(`❌ Endpoint unavailable: ${endpoint}`);
        }
      }

      if (availableEndpoints.length > 0) {
        return {
          success: true,
          message: `Connected successfully. ${availableEndpoints.length} endpoints available.`,
          availableEndpoints,
        };
      } else {
        return {
          success: false,
          message:
            "API key valid but no endpoints accessible. API may have changed.",
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Connection failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    cache.clear();
    console.log("🗑️ Cookie.fun API cache cleared");
  }

  /**
   * Get enhanced cache statistics
   */
  getCacheStats(): {
    size: number;
    keys: string[];
    entries: Array<{ key: string; age: number; ttl: number; expired: boolean }>;
    hitRate?: number;
  } {
    const now = Date.now();
    const entries = Array.from(cache.entries()).map(([key, value]) => ({
      key: key.split(":").slice(1).join(":"), // Remove method prefix for readability
      age: Math.floor((now - value.timestamp) / 1000),
      ttl: Math.floor(value.ttl / 1000),
      expired: now - value.timestamp > value.ttl,
    }));

    return {
      size: cache.size,
      keys: Array.from(cache.keys()),
      entries,
    };
  }

  /**
   * Clean up expired cache entries
   */
  cleanupExpiredCache(): number {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, value] of cache.entries()) {
      if (now - value.timestamp > value.ttl) {
        cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} expired cache entries`);
    }

    return cleanedCount;
  }
}

// Export singleton instance
export const cookieClient = new CookieAPIClient();

// Export class and schemas for use in other files
export {
  CookieAPIClient,
  SectorSchema,
  SmartFollowerSchema,
  AccountFeedTweetSchema,
  ProjectSchema,
  SectorsResponseSchema,
  SmartFollowersResponseSchema,
  AccountFeedResponseSchema,
  ProjectSearchResponseSchema,
};

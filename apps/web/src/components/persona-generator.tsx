"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  Clock,
  RefreshCw,
  Trash2,
  Twitter,
} from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import { trpc } from "@/utils/trpc";
import { Alert, AlertDescription } from "./ui/alert";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./ui/card";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Progress } from "./ui/progress";
import { Separator } from "./ui/separator";

interface PersonaGeneratorProps {
  onPersonaGenerated?: (personaId: string) => void;
}

export function PersonaGenerator({
  onPersonaGenerated,
}: PersonaGeneratorProps) {
  const [twitterHandle, setTwitterHandle] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);

  const router = useRouter();

  // tRPC hooks
  const generateMutation = trpc.persona.generateFromTwitter.useMutation();
  const { data: jobStatus, refetch: refetchJobStatus } =
    trpc.persona.getJobStatus.useQuery(
      { jobId: currentJobId! },
      {
        enabled: !!currentJobId,
        refetchInterval: (data: any) => {
          // Stop polling when job is completed or failed
          if (
            data?.success &&
            (data.job?.status === "completed" || data.job?.status === "failed")
          ) {
            return false;
          }
          return 2000; // Poll every 2 seconds
        },
      }
    );

  const { data: userJobs, refetch: refetchUserJobs } =
    trpc.persona.getUserJobs.useQuery({});
  const { data: personaLimits } = trpc.persona.getPersonaLimits.useQuery();
  const { data: generatedPersonas, refetch: refetchPersonas } =
    trpc.persona.getUserGeneratedPersonas.useQuery();
  const deletePersonaMutation =
    trpc.persona.deleteGeneratedPersona.useMutation();
  const { data: subscription } = trpc.billing.getSubscription.useQuery();

  // Handle job completion
  useEffect(() => {
    if (
      jobStatus?.success &&
      jobStatus.job?.status === "completed" &&
      jobStatus.job.resultPersonality
    ) {
      setIsGenerating(false);
      setCurrentJobId(null);
      toast.success(
        `Persona generated successfully for @${jobStatus.job.twitterHandle}!`
      );
      refetchPersonas();
      refetchUserJobs();
      if (onPersonaGenerated) {
        onPersonaGenerated(jobStatus.job.resultPersonality.id);
      }
    } else if (jobStatus?.success && jobStatus.job?.status === "failed") {
      setIsGenerating(false);
      setCurrentJobId(null);
      toast.error(`Persona generation failed: ${jobStatus.job.errorMessage}`);
      refetchUserJobs();
    }
  }, [jobStatus, onPersonaGenerated, refetchPersonas, refetchUserJobs]);

  // Check if user is on free plan
  const isFreePlan =
    subscription?.clerkPlan?.name === "free" ||
    subscription?.legacyPlan?.name === "free";

  const handleGenerate = async () => {
    if (!twitterHandle.trim()) {
      toast.error("Please enter a Twitter handle");
      return;
    }

    // Check if user is on free plan and show upgrade toast
    if (isFreePlan) {
      toast.error(
        "Persona generation is a premium feature. Please upgrade your plan to continue.",
        {
          action: {
            label: "Upgrade",
            onClick: () => router.push("/profile"),
          },
          duration: 5000,
        }
      );
      return;
    }

    try {
      setIsGenerating(true);
      const result = await generateMutation.mutateAsync({
        twitterHandle: twitterHandle.trim(),
      });

      if (result.success) {
        setCurrentJobId(result.jobId);
        toast.success(result.message);
      }
    } catch (error: any) {
      setIsGenerating(false);
      toast.error(error.message || "Failed to start persona generation");
    }
  };

  const handleDeletePersona = async (
    personaId: string,
    personaName: string
  ) => {
    if (
      !confirm(
        `Are you sure you want to delete "${personaName}"? This action cannot be undone.`
      )
    ) {
      return;
    }

    try {
      const result = await deletePersonaMutation.mutateAsync({ personaId });
      if (result.success) {
        toast.success(result.message);
        refetchPersonas();
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to delete persona");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-blue-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "failed":
        return "bg-red-100 text-red-800";
      case "pending":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-blue-100 text-blue-800";
    }
  };

  const formatStatus = (status: string) => {
    return status.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
  };

  return (
    <div className="space-y-6">
      {/* Generation Form */}
      <Card className="bg-app-card border-app-stroke">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-app-card-heading">
            <Twitter className="h-5 w-5 text-app-main" />
            Generate AI Persona from Twitter
          </CardTitle>
          <CardDescription className="text-app-card-paragraph/70">
            Create a custom AI personality based on someone's Twitter activity.
            We'll analyze their tweets and replies to capture their unique voice
            and style.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Usage Limits */}
          {personaLimits && (
            <Alert className="border-app-stroke bg-app-card">
              <AlertCircle className="h-4 w-4 text-app-main" />
              <AlertDescription className="text-app-card-paragraph">
                {isFreePlan
                  ? "Persona generation is a premium feature. Upgrade your plan to start creating AI personalities."
                  : personaLimits.limits.limit === -1
                    ? "You have unlimited persona generations."
                    : `You have ${personaLimits.limits.remaining} of ${personaLimits.limits.limit} persona generations remaining this month.`}
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="twitter-handle" className="text-app-card-heading">
              Twitter Handle
            </Label>
            <Input
              id="twitter-handle"
              placeholder="@username or username"
              value={twitterHandle}
              onChange={(e) => setTwitterHandle(e.target.value)}
              disabled={isGenerating}
            />
          </div>

          <Button
            onClick={handleGenerate}
            disabled={
              isGenerating ||
              !twitterHandle.trim() ||
              personaLimits?.limits.remaining === 0
            }
            className="w-full"
          >
            {isGenerating ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Generating Persona...
              </>
            ) : isFreePlan ? (
              "Upgrade to Generate Personas"
            ) : (
              "Generate Persona"
            )}
          </Button>

          {/* Current Job Progress */}
          {isGenerating && jobStatus?.success && jobStatus.job && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Progress: {formatStatus(jobStatus.job.status)}</span>
                <span>{jobStatus.job.progress}%</span>
              </div>
              <Progress value={jobStatus.job.progress} className="w-full" />
              <div className="text-xs text-muted-foreground">
                Tweets: {jobStatus.job.tweetsCollected}/
                {jobStatus.job.totalTweetsTarget} • Replies:{" "}
                {jobStatus.job.repliesCollected}/
                {jobStatus.job.totalRepliesTarget}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Generated Personas */}
      {generatedPersonas?.personas && generatedPersonas.personas.length > 0 && (
        <Card className="bg-app-card border-app-stroke">
          <CardHeader>
            <CardTitle className="text-app-card-heading">
              Your Generated Personas
            </CardTitle>
            <CardDescription className="text-app-card-paragraph/70">
              AI personalities created from Twitter analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {generatedPersonas.personas.map((persona: any) => (
                <div
                  key={persona.id}
                  className="flex items-center justify-between p-3 border border-app-stroke rounded-lg bg-app-secondary"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-app-card-heading">
                        {persona.name}
                      </h4>
                      <Badge variant="secondary" className="text-xs">
                        @{persona.sourceTwitterHandle}
                      </Badge>
                    </div>
                    <p className="text-sm text-app-card-paragraph/70 line-clamp-2">
                      {persona.description}
                    </p>
                    <p className="text-xs text-app-card-paragraph/50 mt-1">
                      Created {new Date(persona.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      handleDeletePersona(persona.id, persona.name)
                    }
                    disabled={deletePersonaMutation.isPending}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Jobs */}
      {userJobs?.jobs && userJobs.jobs.length > 0 && (
        <Card className="bg-app-card border-app-stroke">
          <CardHeader>
            <CardTitle className="text-app-card-heading">
              Recent Generation Jobs
            </CardTitle>
            <CardDescription className="text-app-card-paragraph/70">
              Track the progress of your persona generation requests
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {userJobs.jobs.map((job: any) => (
                <div
                  key={job.id}
                  className="flex items-center justify-between p-3 border border-app-stroke rounded-lg bg-app-secondary"
                >
                  <div className="flex items-center gap-3">
                    {getStatusIcon(job.status)}
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-app-card-heading">
                          @{job.twitterHandle}
                        </span>
                        <Badge className={getStatusColor(job.status)}>
                          {formatStatus(job.status)}
                        </Badge>
                      </div>
                      <p className="text-sm text-app-card-paragraph/70">
                        {job.status === "completed" && job.completedAt
                          ? `Completed ${new Date(job.completedAt).toLocaleDateString()}`
                          : `Started ${new Date(job.createdAt).toLocaleDateString()}`}
                      </p>
                      {job.errorMessage && (
                        <p className="text-sm text-red-500 mt-1">
                          {job.errorMessage}
                        </p>
                      )}
                    </div>
                  </div>
                  {job.status !== "completed" && job.status !== "failed" && (
                    <div className="text-right">
                      <div className="text-sm font-medium text-app-card-heading">
                        {job.progress}%
                      </div>
                      <div className="text-xs text-app-card-paragraph/70">
                        {job.tweetsCollected + job.repliesCollected} collected
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
